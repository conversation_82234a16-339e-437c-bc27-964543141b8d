package router

import (
	"ctint-mobile-notification/src/handlers"
	"net/http"
)

type Config struct {
	path    string
	handler func(w http.ResponseWriter, r *http.Request, arg map[string]interface{})
	method  string
}

// 定义路由结构体
type route struct {
	path            string           // 路由的路径
	method          string           // HTTP 方法（如 GET、POST 等）
	handler         http.HandlerFunc // 处理该路由的处理函数
	pathMiddlewares []Middleware     // 路由特定的中间件
}
type Middleware func(http.Handler) http.Handler

// 定义路由组结构体
type routeGroup struct {
	groupMiddlewares []Middleware // 路由组的中间件
	routes           []route      // 路由组包含的路由列表
	groupName        string       // 路由组的名称
}

var configMapV1 = []routeGroup{
	{
		groupName: "index",
		//groupMiddlewares: []Middleware{commonMiddleware.ValidateHeaders, commonMiddleware.ValidateCdssToken, commonMiddleware.InitLogger(commonMiddleware.GlobalLogger), commonMiddleware.CorsMiddleware, commonMiddleware.SetGlobalHeader, commonMiddleware.GlobalErrorHandler},
		routes: []route{
			{
				path:    "",
				method:  http.MethodGet,
				handler: handlers.Index,
			},
		},
	},
	{
		groupName: "notifications",
		routes: []route{
			// 推送通知API
			{
				path:    "/send",
				method:  http.MethodPost,
				handler: handlers.GetNotificationHandler().SendToDevice,
			},
			{
				path:    "/send-batch",
				method:  http.MethodPost,
				handler: handlers.GetNotificationHandler().SendToDevices,
			},
			{
				path:    "/send-topic",
				method:  http.MethodPost,
				handler: handlers.GetNotificationHandler().SendToTopic,
			},
			{
				path:    "/send-condition",
				method:  http.MethodPost,
				handler: handlers.GetNotificationHandler().SendWithCondition,
			},
			{
				path:    "/schedule",
				method:  http.MethodPost,
				handler: handlers.GetNotificationHandler().ScheduleNotification,
			},
			{
				path:    "/{id}/status",
				method:  http.MethodGet,
				handler: handlers.GetNotificationHandler().GetNotificationStatus,
			},
			{
				path:    "/history",
				method:  http.MethodGet,
				handler: func(w http.ResponseWriter, r *http.Request) {
					handler := handlers.GetNotificationHandler()
					if handler == nil {
						http.Error(w, "Handler not initialized", http.StatusInternalServerError)
						return
					}
					handler.GetNotificationHistory(w, r)
				},
			},
		},
	},
	{
		groupName: "devices",
		routes: []route{
			// 设备管理API
			{
				path:    "/register",
				method:  http.MethodPost,
				handler: handlers.GetDeviceHandler().RegisterDevice,
			},
			{
				path:    "/{deviceId}",
				method:  http.MethodPut,
				handler: handlers.GetDeviceHandler().UpdateDevice,
			},
			{
				path:    "/{deviceId}",
				method:  http.MethodDelete,
				handler: handlers.GetDeviceHandler().UnregisterDevice,
			},
			{
				path:    "/{deviceId}",
				method:  http.MethodGet,
				handler: handlers.GetDeviceHandler().GetDevice,
			},
		},
	},
	{
		groupName: "topics",
		routes: []route{
			// 主题管理API
			{
				path:    "/{topicName}/subscribe",
				method:  http.MethodPost,
				handler: handlers.GetTopicHandler().SubscribeToTopic,
			},
			{
				path:    "/{topicName}/unsubscribe",
				method:  http.MethodDelete,
				handler: handlers.GetTopicHandler().UnsubscribeFromTopic,
			},
			{
				path:    "",
				method:  http.MethodGet,
				handler: handlers.GetTopicHandler().GetTopics,
			},
		},
	},
	{
		groupName: "analytics",
		routes: []route{
			// 分析统计API
			{
				path:    "/delivery-metrics",
				method:  http.MethodGet,
				handler: handlers.GetAnalyticsHandler().GetDeliveryMetrics,
			},
			{
				path:    "/performance",
				method:  http.MethodGet,
				handler: handlers.GetAnalyticsHandler().GetPerformanceMetrics,
			},
		},
	},
	{
		groupName: "",
		routes: []route{
			// 健康检查API
			{
				path:    "/health",
				method:  http.MethodGet,
				handler: handlers.GetAnalyticsHandler().GetHealthCheck,
			},
			{
				path:    "/ready",
				method:  http.MethodGet,
				handler: handlers.GetAnalyticsHandler().GetReadinessCheck,
			},
		},
	},
}

var routeConfigMap = map[string][]routeGroup{
	"v1": configMapV1,
}
