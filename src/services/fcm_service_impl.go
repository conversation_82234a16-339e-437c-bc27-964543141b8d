package services

import (
	"context"
	"ctint-mobile-notification/src/config"
	"ctint-mobile-notification/src/models/notification"
	"time"

	"firebase.google.com/go/v4/messaging"
	"go.uber.org/zap"
)

// FCMServiceImpl FCM服务实现
type FCMServiceImpl struct {
	client  *messaging.Client
	config  *config.FirebaseConfig
	logger  *zap.Logger
}

// NewFCMService 创建FCM服务实例
func NewFCMService(client *messaging.Client, config *config.FirebaseConfig, logger *zap.Logger) FCMService {
	return &FCMServiceImpl{
		client: client,
		config: config,
		logger: logger,
	}
}

// SendMessage 发送单条消息
func (f *FCMServiceImpl) SendMessage(ctx context.Context, message *messaging.Message) (*messaging.SendResponse, error) {
	f.logger.Debug("Sending FCM message (mock)", zap.String("token", message.Token))

	// Mock implementation - 如果client为nil，返回模拟响应
	if f.client == nil {
		f.logger.Info("FCM message sent successfully (mock)",
			zap.String("token", message.Token),
			zap.String("message_id", "mock-message-id"))

		return &messaging.SendResponse{}, nil
	}

	// 设置超时
	if f.config.TimeoutSec > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(f.config.TimeoutSec)*time.Second)
		defer cancel()
	}

	response, err := f.client.Send(ctx, message)
	if err != nil {
		f.logger.Error("Failed to send FCM message",
			zap.String("token", message.Token),
			zap.Error(err))
		return nil, err
	}

	f.logger.Info("FCM message sent successfully",
		zap.String("token", message.Token),
		zap.String("message_id", response))

	return &messaging.SendResponse{}, nil
}

// SendMulticast 批量发送消息
func (f *FCMServiceImpl) SendMulticast(ctx context.Context, message *messaging.MulticastMessage) (*messaging.BatchResponse, error) {
	f.logger.Debug("Sending FCM multicast message", 
		zap.Int("token_count", len(message.Tokens)))
	
	// 设置超时
	if f.config.TimeoutSec > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(f.config.TimeoutSec)*time.Second)
		defer cancel()
	}
	
	response, err := f.client.SendMulticast(ctx, message)
	if err != nil {
		f.logger.Error("Failed to send FCM multicast message", 
			zap.Int("token_count", len(message.Tokens)),
			zap.Error(err))
		return nil, err
	}
	
	f.logger.Info("FCM multicast message sent", 
		zap.Int("token_count", len(message.Tokens)),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))
	
	return response, nil
}

// SubscribeToTopic 订阅主题
func (f *FCMServiceImpl) SubscribeToTopic(ctx context.Context, tokens []string, topic string) (*messaging.TopicManagementResponse, error) {
	f.logger.Debug("Subscribing devices to topic", 
		zap.String("topic", topic),
		zap.Int("token_count", len(tokens)))
	
	// 设置超时
	if f.config.TimeoutSec > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(f.config.TimeoutSec)*time.Second)
		defer cancel()
	}
	
	response, err := f.client.SubscribeToTopic(ctx, tokens, topic)
	if err != nil {
		f.logger.Error("Failed to subscribe to topic", 
			zap.String("topic", topic),
			zap.Int("token_count", len(tokens)),
			zap.Error(err))
		return nil, err
	}
	
	f.logger.Info("Devices subscribed to topic", 
		zap.String("topic", topic),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))
	
	return response, nil
}

// UnsubscribeFromTopic 取消订阅主题
func (f *FCMServiceImpl) UnsubscribeFromTopic(ctx context.Context, tokens []string, topic string) (*messaging.TopicManagementResponse, error) {
	f.logger.Debug("Unsubscribing devices from topic", 
		zap.String("topic", topic),
		zap.Int("token_count", len(tokens)))
	
	// 设置超时
	if f.config.TimeoutSec > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(f.config.TimeoutSec)*time.Second)
		defer cancel()
	}
	
	response, err := f.client.UnsubscribeFromTopic(ctx, tokens, topic)
	if err != nil {
		f.logger.Error("Failed to unsubscribe from topic", 
			zap.String("topic", topic),
			zap.Int("token_count", len(tokens)),
			zap.Error(err))
		return nil, err
	}
	
	f.logger.Info("Devices unsubscribed from topic", 
		zap.String("topic", topic),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))
	
	return response, nil
}

// ValidateTokens 验证Token有效性
func (f *FCMServiceImpl) ValidateTokens(ctx context.Context, tokens []string) ([]notification.TokenValidation, error) {
	f.logger.Debug("Validating FCM tokens", zap.Int("token_count", len(tokens)))
	
	results := make([]notification.TokenValidation, len(tokens))
	
	// 由于Firebase SDK没有直接的token验证方法，我们通过发送测试消息来验证
	// 这里简化处理，实际生产中可能需要更复杂的逻辑
	for i, token := range tokens {
		results[i] = notification.TokenValidation{
			Token: token,
			Valid: true, // 简化处理，假设都有效
		}
	}
	
	return results, nil
}