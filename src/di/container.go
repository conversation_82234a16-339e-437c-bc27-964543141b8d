package di

import (
	"ctint-mobile-notification/src/config"
	"ctint-mobile-notification/src/repositories"
	"ctint-mobile-notification/src/services"
	firebaseSDK "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"os"

	"github.com/go-playground/validator/v10"
	"go.uber.org/dig"
	"go.uber.org/zap"
	"sync"
)

// Container 是对 dig.Container 的封装接口
type Container struct {
	Instance *dig.Container
}

// 使用单例模式（或按需初始化）
var globalContainer Container
var once sync.Once

// NewContainer 创建一个新的 Container 实例
func NewContainer() *dig.Container {
	return dig.New()
}

// GetGlobalContainer 返回一个全局的 Container 实例
func GetGlobalContainer() Container {
	once.Do(func() {
		globalContainer.Instance = NewContainer()
		// 注册全局依赖
		registerDependencies(globalContainer.Instance)
	})
	return globalContainer
}

// Invoke 调用容器中的函数
func (c Container) Invoke(function interface{}) error {
	return c.Instance.Invoke(function)
}

// Provide 向容器提供依赖
func (c Container) Provide(constructor interface{}) error {
	return c.Instance.Provide(constructor)
}

// registerDependencies 注册所有依赖项
func registerDependencies(container *dig.Container) {
	// 注册基础组件
	container.Provide(func() *zap.Logger {
		logger, _ := zap.NewDevelopment()
		return logger
	})

	container.Provide(func() *validator.Validate {
		return validator.New()
	})

	// 注册Firebase配置 - 从ProjectConfig或环境变量获取
	container.Provide(func() *config.FirebaseConfig {
		// 先尝试从环境变量获取
		projectID := os.Getenv("FIREBASE_PROJECT_ID")
		credentialsJSON := os.Getenv("FIREBASE_CREDENTIALS_JSON")

		// 如果环境变量为空，设置默认值或从项目配置获取
		if projectID == "" {
			projectID = "test-project-id" // 默认值，应该从配置文件获取
		}

		return &config.FirebaseConfig{
			ProjectID:            projectID,
			CredentialsJSON:      credentialsJSON,
			MaxRetries:           3,
			BatchSize:            500,
			ValidateTokensOnSend: true,
			TimeoutSec:           30,
		}
	})

	// 注册Firebase应用 - 使用mock实现
	container.Provide(func(fbConfig *config.FirebaseConfig) (*firebaseSDK.App, error) {
		// 暂时返回nil，让系统能够启动
		return nil, nil
	})

	// 注册Firebase Messaging客户端 - 使用mock实现
	container.Provide(func(app *firebaseSDK.App) (*messaging.Client, error) {
		// 暂时返回nil，让系统能够启动
		return nil, nil
	})

	// 注册Repository层
	container.Provide(func() repositories.NotificationRepository {
		return repositories.NewMemoryNotificationRepository()
	})

	container.Provide(func() repositories.DeviceRepository {
		return repositories.NewMemoryDeviceRepository()
	})

	container.Provide(func() repositories.TopicSubscriptionRepository {
		return repositories.NewMemoryTopicSubscriptionRepository()
	})

	// 注册Service层
	container.Provide(func(
		client *messaging.Client,
		config *config.FirebaseConfig,
		logger *zap.Logger,
	) services.FCMService {
		return services.NewFCMService(client, config, logger)
	})

	container.Provide(func(
		notificationRepo repositories.NotificationRepository,
		deviceRepo repositories.DeviceRepository,
		fcmService services.FCMService,
		logger *zap.Logger,
	) services.NotificationService {
		return services.NewNotificationService(notificationRepo, deviceRepo, fcmService, logger)
	})

	container.Provide(func(
		deviceRepo repositories.DeviceRepository,
		fcmService services.FCMService,
		logger *zap.Logger,
	) services.DeviceService {
		return services.NewDeviceService(deviceRepo, fcmService, logger)
	})

	container.Provide(func(
		topicRepo repositories.TopicSubscriptionRepository,
		deviceRepo repositories.DeviceRepository,
		fcmService services.FCMService,
		logger *zap.Logger,
	) services.TopicService {
		return services.NewTopicService(topicRepo, deviceRepo, fcmService, logger)
	})
}
