package handlers

import (
	"ctint-mobile-notification/src/di"
	"ctint-mobile-notification/src/services"

	validator "github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

var (
	notificationHandler *NotificationHandler
	deviceHandler       *DeviceHandler  
	topicHandler        *TopicHandler
	analyticsHandler    *AnalyticsHandler
)

// InitHandlers 初始化所有处理器
func InitHandlers(container di.Container) {
	// 从容器获取依赖
	var notificationService services.NotificationService
	var deviceService services.DeviceService
	var topicService services.TopicService
	var validate *validator.Validate
	var logger *zap.Logger

	err := container.Invoke(func(
		ns services.NotificationService,
		ds services.DeviceService,
		ts services.TopicService,
		v *validator.Validate,
		l *zap.Logger,
	) {
		notificationService = ns
		deviceService = ds
		topicService = ts
		validate = v
		logger = l
	})

	if err != nil {
		// 如果依赖注入失败，创建一个基本的logger并打印错误
		basicLogger, _ := zap.NewDevelopment()
		basicLogger.Fatal("Failed to initialize handlers", zap.Error(err))
		return
	}

	// 初始化处理器
	notificationHandler = NewNotificationHandler(notificationService, validate, logger)
	deviceHandler = NewDeviceHandler(deviceService, validate, logger)
	topicHandler = NewTopicHandler(topicService, validate, logger)
	analyticsHandler = NewAnalyticsHandler(logger)
}

// GetNotificationHandler 获取推送通知处理器
func GetNotificationHandler() *NotificationHandler {
	return notificationHandler
}

// GetDeviceHandler 获取设备管理处理器
func GetDeviceHandler() *DeviceHandler {
	return deviceHandler
}

// GetTopicHandler 获取主题管理处理器
func GetTopicHandler() *TopicHandler {
	return topicHandler
}

// GetAnalyticsHandler 获取分析统计处理器
func GetAnalyticsHandler() *AnalyticsHandler {
	return analyticsHandler
}